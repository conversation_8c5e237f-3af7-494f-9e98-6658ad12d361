# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.spyderproject
.spyproject

# Jupyter Notebook
.ipynb_checkpoints

# Data files (保护敏感数据)
data/raw/*.csv
data/raw/*.xlsx
data/raw/*.pkl
data/models/*.pkl
data/models/*.joblib
*.h5

# Logs
logs/*.log
*.log

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*~.nib

# API Keys and Secrets
.env
.env.local
.env.*.local
config/secrets.py
**/secrets.py

# Database
*.db
*.sqlite
*.sqlite3

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Documentation builds
docs/_build/
site/
