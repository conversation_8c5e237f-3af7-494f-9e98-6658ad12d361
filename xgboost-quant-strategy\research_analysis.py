"""
研究分析功能模块
包含因子分析、对比、回测等功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

def analyze_factors(self):
    """分析选中的因子"""
    if not self.selected_factors:
        print("❌ 请先选择因子")
        return
    
    print(f"\n🔍 分析选中的 {len(self.selected_factors)} 个因子")
    print("=" * 50)
    
    factor_data = self.factor_data[self.selected_factors + ['symbol', 'date']].copy()
    
    # 基本统计
    print("\n📊 因子基本统计:")
    stats = factor_data[self.selected_factors].describe()
    print(stats.round(4))
    
    # 缺失值检查
    print("\n🔍 数据质量检查:")
    missing_counts = factor_data[self.selected_factors].isnull().sum()
    total_records = len(factor_data)
    
    for factor in self.selected_factors:
        missing_pct = (missing_counts[factor] / total_records) * 100
        status = "✅" if missing_pct < 5 else "⚠️" if missing_pct < 20 else "❌"
        print(f"  {factor}: {status} 缺失 {missing_counts[factor]} 条 ({missing_pct:.1f}%)")
    
    # 因子分布分析
    print("\n📈 因子分布特征:")
    for factor in self.selected_factors:
        values = factor_data[factor].dropna()
        if len(values) > 0:
            skewness = values.skew()
            kurtosis = values.kurtosis()
            
            skew_desc = "正偏" if skewness > 0.5 else "负偏" if skewness < -0.5 else "对称"
            kurt_desc = "尖峰" if kurtosis > 1 else "平峰" if kurtosis < -1 else "正态"
            
            print(f"  {factor}: {skew_desc}, {kurt_desc} (偏度: {skewness:.2f}, 峰度: {kurtosis:.2f})")
    
    # 询问是否显示图表
    show_plots = input("\n是否显示因子分析图表? (y/n): ").strip().lower()
    if show_plots == 'y':
        plot_factor_analysis(factor_data, self.selected_factors)

def compare_factors(self):
    """因子对比分析"""
    if len(self.selected_factors) < 2:
        print("❌ 请至少选择2个因子进行对比")
        return
    
    print(f"\n📈 对比 {len(self.selected_factors)} 个因子")
    print("=" * 40)
    
    factor_data = self.factor_data[self.selected_factors].copy()
    
    # 相关性分析
    print("\n🔗 因子相关性矩阵:")
    corr_matrix = factor_data.corr()
    print(corr_matrix.round(3))
    
    # 找出高相关性的因子对
    print("\n⚠️ 高相关性因子对 (|相关系数| > 0.7):")
    high_corr_pairs = []
    for i in range(len(self.selected_factors)):
        for j in range(i+1, len(self.selected_factors)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.7:
                factor1 = self.selected_factors[i]
                factor2 = self.selected_factors[j]
                high_corr_pairs.append((factor1, factor2, corr_val))
                print(f"  {factor1} ↔ {factor2}: {corr_val:.3f}")
    
    if not high_corr_pairs:
        print("  ✅ 没有发现高相关性因子对")
    
    # 因子稳定性分析
    print("\n📊 因子稳定性分析:")
    for factor in self.selected_factors:
        values = factor_data[factor].dropna()
        if len(values) > 20:
            # 计算滚动标准差
            rolling_std = values.rolling(20).std().dropna()
            stability = rolling_std.std() / rolling_std.mean() if rolling_std.mean() != 0 else float('inf')
            
            stability_desc = "稳定" if stability < 0.3 else "一般" if stability < 0.6 else "不稳定"
            print(f"  {factor}: {stability_desc} (变异系数: {stability:.3f})")
    
    # 询问是否显示对比图表
    show_plots = input("\n是否显示因子对比图表? (y/n): ").strip().lower()
    if show_plots == 'y':
        plot_factor_comparison(factor_data, corr_matrix, self.selected_factors)

def simple_backtest(self):
    """简单回测"""
    if not self.selected_factors:
        print("❌ 请先选择因子")
        return
    
    print(f"\n🧪 简单回测")
    print("=" * 30)
    
    # 准备回测数据
    print("📊 准备回测数据...")
    
    backtest_data = prepare_backtest_data(self.factor_data, self.raw_data, self.selected_factors)
    
    if backtest_data is None or len(backtest_data) == 0:
        print("❌ 无法准备回测数据")
        return
    
    print(f"✅ 回测数据准备完成: {len(backtest_data)} 条记录")
    
    # 选择回测模型
    print("\n🤖 选择回测模型:")
    print("1. 线性回归")
    print("2. 随机森林")
    print("3. 简单打分模型")
    
    model_choice = input("请选择模型 (1-3): ").strip()
    
    # 执行回测
    results = run_backtest(backtest_data, self.selected_factors, model_choice)
    
    if results:
        print_backtest_results(results)
        
        # 询问是否显示回测图表
        show_plots = input("\n是否显示回测结果图表? (y/n): ").strip().lower()
        if show_plots == 'y':
            plot_backtest_results(results)

def prepare_backtest_data(factor_data, raw_data, selected_factors):
    """准备回测数据"""
    try:
        # 计算未来收益率
        future_returns = []
        
        for symbol in factor_data['symbol'].unique():
            # 获取因子数据
            symbol_factors = factor_data[factor_data['symbol'] == symbol].copy()
            symbol_factors = symbol_factors.sort_values('date').reset_index(drop=True)
            
            # 获取价格数据
            if symbol in raw_data:
                symbol_prices = raw_data[symbol].copy()
                symbol_prices = symbol_prices.sort_values('Date').reset_index(drop=True)
                
                # 计算5日后收益率
                returns_5d = symbol_prices['Close'].pct_change(5).shift(-5)
                
                # 合并数据
                min_len = min(len(symbol_factors), len(returns_5d))
                if min_len > 0:
                    symbol_factors = symbol_factors.iloc[:min_len].copy()
                    symbol_factors['future_return'] = returns_5d.iloc[:min_len].values
                    future_returns.append(symbol_factors)
        
        if future_returns:
            backtest_data = pd.concat(future_returns, ignore_index=True)
            backtest_data = backtest_data.dropna()
            return backtest_data
        else:
            return None
            
    except Exception as e:
        print(f"❌ 数据准备失败: {e}")
        return None

def run_backtest(data, factors, model_choice):
    """执行回测"""
    try:
        X = data[factors]
        y = data['future_return']
        
        # 数据分割
        split_idx = int(len(X) * 0.7)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        if model_choice == '1':
            # 线性回归
            from sklearn.linear_model import LinearRegression
            model = LinearRegression()
            model_name = "线性回归"
        elif model_choice == '2':
            # 随机森林
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(n_estimators=50, random_state=42)
            model_name = "随机森林"
        else:
            # 简单打分模型
            return run_scoring_model(X_train, y_train, X_test, y_test, factors)
        
        # 训练模型
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        
        # 计算评估指标
        from sklearn.metrics import mean_squared_error, r2_score
        
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        direction_acc = np.mean(np.sign(y_test) == np.sign(y_pred))
        
        # 特征重要性
        if hasattr(model, 'feature_importances_'):
            importance = model.feature_importances_
        elif hasattr(model, 'coef_'):
            importance = np.abs(model.coef_)
        else:
            importance = np.ones(len(factors))
        
        return {
            'model_name': model_name,
            'mse': mse,
            'r2': r2,
            'direction_accuracy': direction_acc,
            'feature_importance': dict(zip(factors, importance)),
            'y_true': y_test.values,
            'y_pred': y_pred,
            'train_size': len(X_train),
            'test_size': len(X_test)
        }
        
    except Exception as e:
        print(f"❌ 回测执行失败: {e}")
        return None

def run_scoring_model(X_train, y_train, X_test, y_test, factors):
    """运行简单打分模型"""
    # 计算每个因子与收益率的相关性作为权重
    correlations = {}
    for factor in factors:
        corr = np.corrcoef(X_train[factor], y_train)[0, 1]
        correlations[factor] = abs(corr) if not np.isnan(corr) else 0
    
    # 标准化权重
    total_weight = sum(correlations.values())
    if total_weight > 0:
        weights = {f: w/total_weight for f, w in correlations.items()}
    else:
        weights = {f: 1/len(factors) for f in factors}
    
    # 计算加权得分
    def calculate_score(row):
        return sum(row[factor] * weights[factor] for factor in factors)
    
    y_pred = X_test.apply(calculate_score, axis=1)
    
    # 评估
    mse = np.mean((y_test - y_pred) ** 2)
    r2 = 1 - (np.sum((y_test - y_pred) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2))
    direction_acc = np.mean(np.sign(y_test) == np.sign(y_pred))
    
    return {
        'model_name': '简单打分模型',
        'mse': mse,
        'r2': r2,
        'direction_accuracy': direction_acc,
        'feature_importance': correlations,
        'y_true': y_test.values,
        'y_pred': y_pred.values,
        'train_size': len(X_train),
        'test_size': len(X_test)
    }

def print_backtest_results(results):
    """打印回测结果"""
    print(f"\n📊 {results['model_name']} 回测结果:")
    print(f"  样本数: 训练集 {results['train_size']}, 测试集 {results['test_size']}")
    print(f"  MSE: {results['mse']:.6f}")
    print(f"  R²: {results['r2']:.4f}")
    print(f"  方向准确率: {results['direction_accuracy']:.2%}")
    
    print(f"\n🔍 因子重要性排序:")
    sorted_importance = sorted(results['feature_importance'].items(), 
                              key=lambda x: x[1], reverse=True)
    for factor, importance in sorted_importance:
        print(f"  {factor}: {importance:.4f}")

def restart_data_selection(self):
    """重新开始数据选择"""
    print("\n🔄 重新开始数据选择流程")
    
    # 清空现有数据
    self.data_source = None
    self.stock_pool = []
    self.time_range = {}
    self.raw_data = {}
    self.factor_data = None
    self.selected_factors = []
    
    print("✅ 数据已清空，重新开始...")

def save_results(self):
    """保存研究结果"""
    if not self.selected_factors:
        print("❌ 没有研究结果可保存")
        return
    
    print("\n💾 保存研究结果")
    
    # 创建结果字典
    results = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data_source': self.data_source,
        'stock_pool': self.stock_pool,
        'time_range': self.time_range,
        'selected_factors': self.selected_factors,
        'data_summary': {
            'total_stocks': len(self.stock_pool),
            'total_records': sum(len(df) for df in self.raw_data.values()),
            'factor_count': len(self.selected_factors),
            'factor_data_records': len(self.factor_data) if self.factor_data is not None else 0
        }
    }
    
    # 保存到JSON文件
    filename = f"research_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        # 处理不能序列化的对象
        results_copy = results.copy()
        if 'api' in results_copy['data_source']:
            del results_copy['data_source']['api']
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results_copy, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 研究结果已保存到: {filename}")
        
        # 保存因子数据
        if self.factor_data is not None:
            factor_filename = f"factor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            self.factor_data.to_csv(factor_filename, index=False, encoding='utf-8')
            print(f"✅ 因子数据已保存到: {factor_filename}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")

# 绘图函数
def plot_factor_analysis(factor_data, selected_factors):
    """绘制因子分析图"""
    try:
        n_factors = min(len(selected_factors), 6)  # 最多显示6个因子
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('因子分析图表', fontsize=16)
        
        for i, factor in enumerate(selected_factors[:n_factors]):
            row, col = i // 3, i % 3
            
            values = factor_data[factor].dropna()
            if len(values) > 0:
                axes[row, col].hist(values, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
                axes[row, col].set_title(f'{factor} 分布')
                axes[row, col].set_xlabel('值')
                axes[row, col].set_ylabel('频数')
        
        # 隐藏多余的子图
        for i in range(n_factors, 6):
            row, col = i // 3, i % 3
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"❌ 绘图失败: {e}")

def plot_factor_comparison(factor_data, corr_matrix, selected_factors):
    """绘制因子对比图"""
    try:
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 相关性热力图
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, fmt='.2f', ax=axes[0])
        axes[0].set_title('因子相关性热力图')
        
        # 因子分布对比
        for factor in selected_factors[:5]:  # 最多显示5个因子
            values = factor_data[factor].dropna()
            if len(values) > 0:
                axes[1].hist(values, alpha=0.5, label=factor, bins=20)
        
        axes[1].set_title('因子分布对比')
        axes[1].set_xlabel('值')
        axes[1].set_ylabel('频数')
        axes[1].legend()
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"❌ 绘图失败: {e}")

def plot_backtest_results(results):
    """绘制回测结果图"""
    try:
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle(f'{results["model_name"]} 回测结果', fontsize=16)
        
        y_true = results['y_true']
        y_pred = results['y_pred']
        
        # 预测vs实际散点图
        axes[0, 0].scatter(y_true, y_pred, alpha=0.6)
        axes[0, 0].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('实际收益率')
        axes[0, 0].set_ylabel('预测收益率')
        axes[0, 0].set_title('预测 vs 实际')
        
        # 残差图
        residuals = y_true - y_pred
        axes[0, 1].scatter(y_pred, residuals, alpha=0.6)
        axes[0, 1].axhline(y=0, color='r', linestyle='--')
        axes[0, 1].set_xlabel('预测收益率')
        axes[0, 1].set_ylabel('残差')
        axes[0, 1].set_title('残差图')
        
        # 时间序列对比
        axes[1, 0].plot(y_true, label='实际', alpha=0.7)
        axes[1, 0].plot(y_pred, label='预测', alpha=0.7)
        axes[1, 0].set_xlabel('时间')
        axes[1, 0].set_ylabel('收益率')
        axes[1, 0].set_title('时间序列对比')
        axes[1, 0].legend()
        
        # 特征重要性
        importance = results['feature_importance']
        factors = list(importance.keys())
        values = list(importance.values())
        
        axes[1, 1].barh(factors, values)
        axes[1, 1].set_xlabel('重要性')
        axes[1, 1].set_title('因子重要性')
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"❌ 绘图失败: {e}")
