{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# XGBoost多因子量化交易策略演示\n", "\n", "本笔记本演示如何使用我们开发的XGBoost多因子量化交易策略系统。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "\n", "# 导入自定义模块\n", "from config.settings import Config\n", "from data.data_loader import DataLoader\n", "from data.factor_engine import FactorEngine\n", "from models.feature_engineer import FeatureEngineer\n", "from models.xgboost_model import XGBoostModel\n", "from utils.helpers import load_pickle, load_json\n", "\n", "# 设置图表样式\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ 库导入完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 加载已训练的模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载模型\n", "model = XGBoostModel()\n", "model.load_model('../data/models/xgboost_model.pkl')\n", "\n", "# 加载特征工程器\n", "feature_engineer = FeatureEngineer()\n", "feature_engineer.load_feature_engineer('../data/models/feature_engineer.pkl')\n", "\n", "# 加载训练结果\n", "results = load_json('../data/models/training_results.json')\n", "\n", "print(\"✅ 模型加载完成\")\n", "print(f\"模型参数: {model.params}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 分析模型性能"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 显示测试集性能\n", "print(\"📊 测试集性能指标:\")\n", "for metric, value in results['test_metrics'].items():\n", "    print(f\"  {metric}: {value:.4f}\")\n", "\n", "print(\"\\n📊 交叉验证性能指标:\")\n", "cv_metrics = results['cv_result']['avg_metrics']\n", "for metric, value in cv_metrics.items():\n", "    if 'mean_' in metric:\n", "        clean_metric = metric.replace('mean_', '')\n", "        print(f\"  {clean_metric}: {value:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 特征重要性分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取特征重要性\n", "importance_df = pd.DataFrame(results['feature_importance'])\n", "top_features = importance_df.head(15)\n", "\n", "# 绘制特征重要性图\n", "plt.figure(figsize=(12, 8))\n", "plt.barh(range(len(top_features)), top_features['importance'])\n", "plt.yticks(range(len(top_features)), top_features['feature'])\n", "plt.xlabel('Feature Importance')\n", "plt.title('Top 15 Feature Importance')\n", "plt.gca().invert_yaxis()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 Top 10 重要特征:\")\n", "for i, row in top_features.head(10).iterrows():\n", "    print(f\"  {i+1}. {row['feature']}: {row['importance']:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 单只股票预测演示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化数据加载器和因子引擎\n", "data_loader = DataLoader()\n", "factor_engine = FactorEngine()\n", "\n", "# 获取单只股票数据\n", "symbol = '000001.SZ'\n", "end_date = datetime.now().strftime('%Y-%m-%d')\n", "start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')\n", "\n", "print(f\"📈 获取 {symbol} 数据...\")\n", "stock_data = data_loader.get_stock_data(symbol, start_date, end_date)\n", "print(f\"数据范围: {stock_data.index[0]} 到 {stock_data.index[-1]}\")\n", "print(f\"数据条数: {len(stock_data)}\")\n", "\n", "# 显示价格走势\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(stock_data.index, stock_data['close'], label='收盘价')\n", "plt.title(f'{symbol} 价格走势')\n", "plt.xlabel('日期')\n", "plt.ylabel('价格')\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 因子计算和预测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算因子\n", "print(\"🔧 计算技术因子...\")\n", "factors = factor_engine.calculate_technical_factors(stock_data)\n", "print(f\"计算了 {factors.shape[1]} 个技术因子\")\n", "\n", "# 预处理因子\n", "factors_processed = factor_engine.preprocess_factors(factors)\n", "factors_processed = factors_processed.dropna()\n", "\n", "if len(factors_processed) > 0:\n", "    # 选择数值列\n", "    numeric_columns = factors_processed.select_dtypes(include=[np.number]).columns\n", "    factors_numeric = factors_processed[numeric_columns]\n", "    \n", "    # 应用特征工程（仅缩放，不重新选择特征）\n", "    factors_scaled = feature_engineer.scale_features(factors_numeric, fit=False)\n", "    \n", "    # 选择训练时使用的特征\n", "    selected_features = feature_engineer.selected_features\n", "    available_features = [f for f in selected_features if f in factors_scaled.columns]\n", "    \n", "    if len(available_features) > 10:  # 确保有足够的特征\n", "        factors_final = factors_scaled[available_features]\n", "        \n", "        # 进行预测\n", "        print(\"🔮 进行预测...\")\n", "        predictions = model.predict(factors_final)\n", "        \n", "        # 显示最近的预测结果\n", "        recent_data = factors_final.tail(10)\n", "        recent_predictions = predictions[-10:]\n", "        recent_prices = stock_data['close'].tail(10)\n", "        \n", "        print(\"\\n📊 最近10天的预测结果:\")\n", "        for i, (date, pred, price) in enumerate(zip(recent_data.index, recent_predictions, recent_prices)):\n", "            print(f\"  {date.strftime('%Y-%m-%d')}: 预测收益率 {pred:.4f}, 当前价格 {price:.2f}\")\n", "    else:\n", "        print(\"⚠️ 可用特征不足，无法进行预测\")\nelse:\n", "    print(\"⚠️ 因子数据不足，无法进行预测\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 策略信号生成示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 简单的信号生成逻辑\n", "if 'predictions' in locals() and len(predictions) > 0:\n", "    # 设置信号阈值\n", "    buy_threshold = 0.02   # 预测收益率大于2%时买入\n", "    sell_threshold = -0.02 # 预测收益率小于-2%时卖出\n", "    \n", "    # 生成交易信号\n", "    signals = []\n", "    for pred in predictions:\n", "        if pred > buy_threshold:\n", "            signals.append('买入')\n", "        elif pred < sell_threshold:\n", "            signals.append('卖出')\n", "        else:\n", "            signals.append('持有')\n", "    \n", "    # 统计信号分布\n", "    signal_counts = pd.Series(signals).value_counts()\n", "    print(\"📊 交易信号分布:\")\n", "    for signal, count in signal_counts.items():\n", "        print(f\"  {signal}: {count} 次 ({count/len(signals)*100:.1f}%)\")\n", "    \n", "    # 显示最近的信号\n", "    print(\"\\n📈 最近10天的交易信号:\")\n", "    recent_signals = signals[-10:]\n", "    for i, (date, signal, pred) in enumerate(zip(recent_data.index, recent_signals, recent_predictions)):\n", "        print(f\"  {date.strftime('%Y-%m-%d')}: {signal} (预测: {pred:.4f})\")\nelse:\n", "    print(\"⚠️ 无预测数据，无法生成信号\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 总结"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"🎉 XGBoost多因子量化交易策略演示完成!\")\n", "print(\"\\n📋 系统特点:\")\n", "print(\"  ✅ 多因子模型: 结合技术指标和基本面指标\")\n", "print(\"  ✅ 机器学习: 使用XGBoost算法进行预测\")\n", "print(\"  ✅ 特征工程: 包含特征选择、交互特征、标准化\")\n", "print(\"  ✅ 风险控制: 时间序列验证避免未来信息泄露\")\n", "print(\"  ✅ 模块化设计: 易于扩展和维护\")\n", "print(\"\\n📈 下一步可以:\")\n", "print(\"  1. 开发完整的回测框架\")\n", "print(\"  2. 实现组合优化算法\")\n", "print(\"  3. 添加更多风险控制机制\")\n", "print(\"  4. 连接实盘交易接口\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}