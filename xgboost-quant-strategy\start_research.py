"""
简化启动脚本 - 交互式多因子研究工具
解决依赖问题，提供最小化运行环境
"""

import sys
import os

def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        'pandas',
        'numpy', 
        'matplotlib',
        'seaborn',
        'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {missing_packages}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def install_minimal_deps():
    """安装最小依赖"""
    print("🔧 正在安装最小依赖包...")
    
    minimal_packages = [
        'pandas>=1.5.0',
        'numpy>=1.20.0',
        'matplotlib>=3.5.0',
        'seaborn>=0.11.0',
        'scikit-learn>=1.0.0'
    ]
    
    import subprocess
    
    for package in minimal_packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def create_mock_modules():
    """创建模拟模块以避免复杂依赖"""
    
    # 创建简化的数据加载器
    mock_data_loader = '''
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class DataLoader:
    def get_stock_list(self):
        """返回模拟股票列表"""
        return pd.DataFrame()
    
    def get_stock_data(self, symbol, start_date, end_date):
        """生成模拟股票数据"""
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日
        
        n = len(dates)
        if n == 0:
            return pd.DataFrame()
        
        # 生成模拟价格数据
        np.random.seed(hash(symbol) % 2**32)
        
        base_price = 100
        returns = np.random.normal(0.001, 0.02, n)
        prices = base_price * np.exp(np.cumsum(returns))
        
        # 生成OHLC数据
        high_factor = 1 + np.abs(np.random.normal(0, 0.01, n))
        low_factor = 1 - np.abs(np.random.normal(0, 0.01, n))
        
        data = pd.DataFrame({
            'Open': prices * np.random.uniform(0.99, 1.01, n),
            'High': prices * high_factor,
            'Low': prices * low_factor,
            'Close': prices,
            'Volume': np.random.randint(1000000, 10000000, n)
        }, index=dates)
        
        return data
'''
    
    # 创建简化的因子引擎
    mock_factor_engine = '''
import pandas as pd
import numpy as np

class FactorEngine:
    def calculate_technical_factors(self, data):
        """计算技术因子"""
        factors = pd.DataFrame(index=data.index)
        
        # 移动平均类
        for period in [5, 10, 20, 60, 120]:
            factors[f'sma_{period}'] = data['Close'].rolling(period).mean()
            factors[f'ema_{period}'] = data['Close'].ewm(span=period).mean()
        
        # 动量类
        factors['momentum_5'] = data['Close'].pct_change(5)
        factors['momentum_10'] = data['Close'].pct_change(10)
        factors['momentum_20'] = data['Close'].pct_change(20)
        
        # RSI
        delta = data['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        factors['rsi_14'] = 100 - (100 / (1 + rs))
        
        # 波动率
        for period in [5, 10, 20, 60]:
            factors[f'volatility_{period}'] = data['Close'].pct_change().rolling(period).std()
            factors[f'hv_{period}'] = data['Close'].pct_change().rolling(period).std() * np.sqrt(252)
        
        # 成交量相关
        factors['volume_ma_5'] = data['Volume'].rolling(5).mean()
        factors['volume_ma_20'] = data['Volume'].rolling(20).mean()
        factors['volume_ma_60'] = data['Volume'].rolling(60).mean()
        factors['volume_std_20'] = data['Volume'].rolling(20).std()
        factors['volume_std_60'] = data['Volume'].rolling(60).std()
        
        # VWAP
        factors['vwap_5'] = (data['Close'] * data['Volume']).rolling(5).sum() / data['Volume'].rolling(5).sum()
        factors['vwap_20'] = (data['Close'] * data['Volume']).rolling(20).sum() / data['Volume'].rolling(20).sum()
        
        # OBV
        obv = (data['Volume'] * np.sign(data['Close'].diff())).cumsum()
        factors['obv'] = obv
        
        # MACD
        ema12 = data['Close'].ewm(span=12).mean()
        ema26 = data['Close'].ewm(span=26).mean()
        factors['macd'] = ema12 - ema26
        factors['macd_signal'] = factors['macd'].ewm(span=9).mean()
        factors['macd_hist'] = factors['macd'] - factors['macd_signal']
        
        # 布林带
        sma20 = data['Close'].rolling(20).mean()
        std20 = data['Close'].rolling(20).std()
        factors['bb_upper'] = sma20 + 2 * std20
        factors['bb_lower'] = sma20 - 2 * std20
        factors['bb_width'] = factors['bb_upper'] - factors['bb_lower']
        factors['bb_position'] = (data['Close'] - factors['bb_lower']) / factors['bb_width']
        
        # ATR
        high_low = data['High'] - data['Low']
        high_close = np.abs(data['High'] - data['Close'].shift())
        low_close = np.abs(data['Low'] - data['Close'].shift())
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        factors['atr_14'] = true_range.rolling(14).mean()
        
        # CCI
        tp = (data['High'] + data['Low'] + data['Close']) / 3
        sma_tp = tp.rolling(20).mean()
        mad = tp.rolling(20).apply(lambda x: np.abs(x - x.mean()).mean())
        factors['cci_20'] = (tp - sma_tp) / (0.015 * mad)
        
        return factors
    
    def calculate_fundamental_factors(self, data):
        """计算基本面因子（模拟）"""
        factors = pd.DataFrame(index=data.index)
        
        # 模拟基本面数据
        factors['pe_ratio'] = np.random.uniform(10, 30, len(data))
        factors['pb_ratio'] = np.random.uniform(1, 5, len(data))
        factors['roe'] = np.random.uniform(0.05, 0.25, len(data))
        factors['debt_ratio'] = np.random.uniform(0.2, 0.8, len(data))
        factors['current_ratio'] = np.random.uniform(1, 3, len(data))
        factors['quick_ratio'] = np.random.uniform(0.5, 2, len(data))
        factors['gross_margin'] = np.random.uniform(0.1, 0.5, len(data))
        factors['net_margin'] = np.random.uniform(0.02, 0.15, len(data))
        
        return factors
'''
    
    # 创建目录
    os.makedirs('data', exist_ok=True)
    os.makedirs('utils', exist_ok=True)
    
    # 写入模拟模块
    with open('data/__init__.py', 'w') as f:
        f.write('')
    
    with open('data/data_loader.py', 'w') as f:
        f.write(mock_data_loader)
    
    with open('data/factor_engine.py', 'w') as f:
        f.write(mock_factor_engine)
    
    # 创建简化的其他模块
    with open('data/data_processor.py', 'w') as f:
        f.write('class DataProcessor:\n    pass\n')
    
    with open('models/__init__.py', 'w') as f:
        f.write('')
    
    with open('models/feature_engineer.py', 'w') as f:
        f.write('class FeatureEngineer:\n    pass\n')
    
    with open('models/xgboost_model.py', 'w') as f:
        f.write('class XGBoostModel:\n    pass\n')
    
    with open('utils/__init__.py', 'w') as f:
        f.write('')
    
    with open('utils/logger.py', 'w') as f:
        f.write('''
class LoggerManager:
    @staticmethod
    def get_logger(name):
        import logging
        return logging.getLogger(name)
''')
    
    print("✅ 模拟模块创建完成")

def main():
    """主启动函数"""
    print("🎯 交互式多因子研究工具 - 启动检查")
    print("=" * 50)
    
    # 检查依赖
    print("\n📦 检查依赖包...")
    if not check_dependencies():
        install_choice = input("\n是否自动安装缺少的依赖? (y/n): ").strip().lower()
        if install_choice == 'y':
            if not install_minimal_deps():
                print("❌ 依赖安装失败，程序退出")
                return
        else:
            print("❌ 缺少必要依赖，程序退出")
            return
    
    # 创建模拟模块
    print("\n🔧 准备运行环境...")
    create_mock_modules()
    
    # 启动主程序
    print("\n🚀 启动交互式多因子研究工具...")
    try:
        from interactive_factor_research import main as research_main
        research_main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
