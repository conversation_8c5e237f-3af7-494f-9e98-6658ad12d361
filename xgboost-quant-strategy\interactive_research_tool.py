"""
完整的交互式多因子研究工具
支持数据源选择、股票选择、因子选择的全流程交互
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class InteractiveResearchTool:
    """完整的交互式多因子研究工具"""
    
    def __init__(self):
        self.data_source = None
        self.stock_pool = []
        self.time_range = {}
        self.raw_data = {}
        self.factor_data = None
        self.selected_factors = []
        
        print("🎯 完整交互式多因子研究工具")
        print("=" * 50)
        print("支持数据源选择、股票选择、因子选择的全流程交互")
    
    def show_welcome(self):
        """显示欢迎界面"""
        print("""
🎯 欢迎使用完整交互式多因子研究工具！

本工具提供完整的研究流程：
1. 📊 选择数据源（本地文件/在线API/模拟数据）
2. 🏢 选择股票池（手动输入/文件导入/预设池）
3. 📅 设置时间范围
4. 👀 数据预览和质量检查
5. 🔧 计算和选择因子
6. 📈 因子分析和对比
7. 🧪 交互式回测

让我们开始您的多因子研究之旅！
        """)
    
    def select_data_source(self):
        """选择数据源"""
        print("\n📊 数据源选择")
        print("=" * 40)
        print("请选择您的数据源：")
        print("1. 📁 本地CSV文件")
        print("2. 🌐 在线API（Yahoo Finance）")
        print("3. 🎲 模拟数据（用于演示）")
        print("4. 📋 手动输入数据")
        
        while True:
            choice = input("\n请选择数据源 (1-4): ").strip()
            
            if choice == '1':
                return self.setup_local_files()
            elif choice == '2':
                return self.setup_online_api()
            elif choice == '3':
                return self.setup_mock_data()
            elif choice == '4':
                return self.setup_manual_data()
            else:
                print("❌ 无效选择，请重新输入")
    
    def setup_local_files(self):
        """设置本地文件数据源"""
        print("\n📁 本地CSV文件设置")
        print("-" * 30)
        
        data_dir = input("请输入数据文件夹路径（默认: ./data）: ").strip()
        if not data_dir:
            data_dir = "./data"
        
        if not os.path.exists(data_dir):
            print(f"❌ 路径不存在: {data_dir}")
            create_dir = input("是否创建示例数据? (y/n): ").strip().lower()
            if create_dir == 'y':
                self.create_sample_csv_data(data_dir)
            return False
        
        # 扫描CSV文件
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        
        if not csv_files:
            print(f"❌ 在 {data_dir} 中未找到CSV文件")
            create_sample = input("是否创建示例数据? (y/n): ").strip().lower()
            if create_sample == 'y':
                self.create_sample_csv_data(data_dir)
                csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        
        print(f"\n找到 {len(csv_files)} 个CSV文件:")
        for i, file in enumerate(csv_files, 1):
            print(f"  {i}. {file}")
        
        self.data_source = {
            'type': 'local_csv',
            'path': data_dir,
            'files': csv_files
        }
        
        print(f"✅ 本地CSV数据源设置完成")
        return True
    
    def setup_online_api(self):
        """设置在线API数据源"""
        print("\n🌐 在线API设置")
        print("-" * 20)
        
        print("支持的API:")
        print("1. Yahoo Finance (免费，全球股票)")
        print("2. 模拟API（演示用）")
        
        api_choice = input("请选择API (1-2): ").strip()
        
        if api_choice == '1':
            try:
                import yfinance as yf
                print("✅ Yahoo Finance API 可用")
                self.data_source = {
                    'type': 'yahoo_finance',
                    'api': yf
                }
                return True
            except ImportError:
                print("❌ 需要安装 yfinance: pip install yfinance")
                use_mock = input("是否使用模拟API? (y/n): ").strip().lower()
                if use_mock == 'y':
                    self.data_source = {
                        'type': 'mock_api'
                    }
                    return True
                return False
        else:
            self.data_source = {
                'type': 'mock_api'
            }
            print("✅ 模拟API设置完成")
            return True
    
    def setup_mock_data(self):
        """设置模拟数据"""
        print("\n🎲 模拟数据设置")
        print("-" * 20)
        
        num_stocks = input("请输入股票数量（默认: 5）: ").strip()
        num_stocks = int(num_stocks) if num_stocks.isdigit() else 5
        
        days = input("请输入数据天数（默认: 252）: ").strip()
        days = int(days) if days.isdigit() else 252
        
        self.data_source = {
            'type': 'mock_data',
            'num_stocks': num_stocks,
            'days': days
        }
        
        print(f"✅ 模拟数据设置完成: {num_stocks}只股票，{days}天数据")
        return True
    
    def setup_manual_data(self):
        """设置手动输入数据"""
        print("\n📋 手动数据输入")
        print("-" * 20)
        print("您可以手动输入少量数据进行测试")
        
        self.data_source = {
            'type': 'manual_input'
        }
        
        print("✅ 手动输入模式设置完成")
        return True
    
    def create_sample_csv_data(self, data_dir):
        """创建示例CSV数据"""
        print(f"📁 创建示例数据到 {data_dir}...")
        
        os.makedirs(data_dir, exist_ok=True)
        
        # 生成示例股票数据
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 工作日
        
        for i, symbol in enumerate(symbols):
            np.random.seed(i)  # 确保可重复
            
            n = len(dates)
            returns = np.random.normal(0.001, 0.02, n)
            prices = 100 * np.exp(np.cumsum(returns))
            
            data = pd.DataFrame({
                'Date': dates,
                'Open': prices * np.random.uniform(0.99, 1.01, n),
                'High': prices * (1 + np.abs(np.random.normal(0, 0.01, n))),
                'Low': prices * (1 - np.abs(np.random.normal(0, 0.01, n))),
                'Close': prices,
                'Volume': np.random.randint(1000000, 10000000, n)
            })
            
            filename = os.path.join(data_dir, f"{symbol}.csv")
            data.to_csv(filename, index=False)
            print(f"  ✅ 创建 {filename}")
        
        print(f"✅ 示例数据创建完成")
    
    def select_stock_pool(self):
        """选择股票池"""
        print("\n🏢 股票池选择")
        print("=" * 30)
        
        if self.data_source['type'] == 'local_csv':
            return self.select_from_csv_files()
        elif self.data_source['type'] in ['yahoo_finance', 'mock_api']:
            return self.select_from_symbol_input()
        elif self.data_source['type'] == 'mock_data':
            return self.select_mock_stocks()
        elif self.data_source['type'] == 'manual_input':
            return self.input_manual_data()
    
    def select_from_csv_files(self):
        """从CSV文件选择股票"""
        print("可用的股票文件:")
        files = self.data_source['files']
        
        for i, file in enumerate(files, 1):
            symbol = file.replace('.csv', '')
            print(f"  {i}. {symbol}")
        
        print("\n选择方式:")
        print("1. 全部选择")
        print("2. 按序号选择")
        print("3. 按名称选择")
        
        choice = input("请选择方式 (1-3): ").strip()
        
        if choice == '1':
            self.stock_pool = [f.replace('.csv', '') for f in files]
        elif choice == '2':
            indices = input("请输入序号（用逗号分隔）: ").strip()
            try:
                selected_indices = [int(i.strip()) - 1 for i in indices.split(',')]
                self.stock_pool = [files[i].replace('.csv', '') for i in selected_indices if 0 <= i < len(files)]
            except:
                print("❌ 输入格式错误")
                return False
        elif choice == '3':
            symbols = input("请输入股票代码（用逗号分隔）: ").strip()
            self.stock_pool = [s.strip() for s in symbols.split(',')]
        
        print(f"✅ 选择了 {len(self.stock_pool)} 只股票: {self.stock_pool}")
        return True
    
    def select_from_symbol_input(self):
        """通过输入股票代码选择"""
        print("请输入股票代码:")
        print("示例: AAPL,MSFT,GOOGL,TSLA,NVDA")
        
        symbols = input("股票代码（用逗号分隔）: ").strip()
        if symbols:
            self.stock_pool = [s.strip().upper() for s in symbols.split(',')]
            print(f"✅ 选择了 {len(self.stock_pool)} 只股票: {self.stock_pool}")
            return True
        else:
            print("❌ 请输入至少一个股票代码")
            return False
    
    def select_mock_stocks(self):
        """选择模拟股票"""
        num_stocks = self.data_source['num_stocks']
        self.stock_pool = [f"股票{chr(65+i)}" for i in range(num_stocks)]
        print(f"✅ 生成 {len(self.stock_pool)} 只模拟股票: {self.stock_pool}")
        return True
    
    def input_manual_data(self):
        """手动输入数据"""
        print("手动数据输入模式")
        print("请输入股票代码和对应的价格数据")
        
        symbol = input("股票代码: ").strip()
        if not symbol:
            print("❌ 请输入股票代码")
            return False
        
        print("请输入价格数据（格式: 日期,开盘,最高,最低,收盘,成交量）")
        print("示例: 2024-01-01,100,102,99,101,1000000")
        print("输入 'done' 完成输入")
        
        data_rows = []
        while True:
            row = input("数据行: ").strip()
            if row.lower() == 'done':
                break
            
            try:
                parts = row.split(',')
                if len(parts) == 6:
                    data_rows.append(parts)
                else:
                    print("❌ 格式错误，请重新输入")
            except:
                print("❌ 数据格式错误")
        
        if data_rows:
            # 构造数据
            df = pd.DataFrame(data_rows, columns=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])
            df['Date'] = pd.to_datetime(df['Date'])
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col])
            
            self.raw_data[symbol] = df
            self.stock_pool = [symbol]
            print(f"✅ 手动输入完成: {symbol}, {len(df)} 条记录")
            return True
        else:
            print("❌ 没有输入有效数据")
            return False

    def set_time_range(self):
        """设置时间范围"""
        print("\n📅 时间范围设置")
        print("=" * 30)

        print("请选择时间范围设置方式:")
        print("1. 预设范围（最近1年、2年等）")
        print("2. 自定义日期范围")
        print("3. 使用全部可用数据")

        choice = input("请选择 (1-3): ").strip()

        if choice == '1':
            return self.set_preset_range()
        elif choice == '2':
            return self.set_custom_range()
        elif choice == '3':
            self.time_range = {'type': 'all'}
            print("✅ 将使用全部可用数据")
            return True
        else:
            print("❌ 无效选择")
            return False

    def set_preset_range(self):
        """设置预设时间范围"""
        print("\n预设时间范围:")
        print("1. 最近1个月")
        print("2. 最近3个月")
        print("3. 最近6个月")
        print("4. 最近1年")
        print("5. 最近2年")
        print("6. 最近3年")

        choice = input("请选择 (1-6): ").strip()

        end_date = datetime.now()

        if choice == '1':
            start_date = end_date - timedelta(days=30)
            period = "1个月"
        elif choice == '2':
            start_date = end_date - timedelta(days=90)
            period = "3个月"
        elif choice == '3':
            start_date = end_date - timedelta(days=180)
            period = "6个月"
        elif choice == '4':
            start_date = end_date - timedelta(days=365)
            period = "1年"
        elif choice == '5':
            start_date = end_date - timedelta(days=730)
            period = "2年"
        elif choice == '6':
            start_date = end_date - timedelta(days=1095)
            period = "3年"
        else:
            print("❌ 无效选择")
            return False

        self.time_range = {
            'type': 'preset',
            'start_date': start_date,
            'end_date': end_date,
            'period': period
        }

        print(f"✅ 时间范围设置: 最近{period} ({start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')})")
        return True

    def set_custom_range(self):
        """设置自定义时间范围"""
        print("\n自定义时间范围:")

        start_str = input("请输入开始日期 (YYYY-MM-DD): ").strip()
        end_str = input("请输入结束日期 (YYYY-MM-DD, 默认今天): ").strip()

        try:
            start_date = datetime.strptime(start_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_str, '%Y-%m-%d') if end_str else datetime.now()

            if start_date >= end_date:
                print("❌ 开始日期必须早于结束日期")
                return False

            self.time_range = {
                'type': 'custom',
                'start_date': start_date,
                'end_date': end_date
            }

            print(f"✅ 自定义时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            return True

        except ValueError:
            print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
            return False

    def load_data(self):
        """加载数据"""
        print("\n📊 加载数据")
        print("=" * 20)

        if self.data_source['type'] == 'local_csv':
            return self.load_csv_data()
        elif self.data_source['type'] == 'yahoo_finance':
            return self.load_yahoo_data()
        elif self.data_source['type'] == 'mock_api':
            return self.load_mock_api_data()
        elif self.data_source['type'] == 'mock_data':
            return self.load_mock_data()
        elif self.data_source['type'] == 'manual_input':
            print("✅ 手动输入的数据已加载")
            return True

    def load_csv_data(self):
        """加载CSV数据"""
        data_path = self.data_source['path']

        for symbol in self.stock_pool:
            filename = os.path.join(data_path, f"{symbol}.csv")

            if os.path.exists(filename):
                try:
                    df = pd.read_csv(filename)
                    df['Date'] = pd.to_datetime(df['Date'])
                    df = df.sort_values('Date')

                    # 应用时间范围过滤
                    if self.time_range.get('type') != 'all':
                        start_date = self.time_range.get('start_date')
                        end_date = self.time_range.get('end_date')
                        if start_date and end_date:
                            df = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)]

                    if len(df) > 0:
                        self.raw_data[symbol] = df
                        print(f"✅ {symbol}: {len(df)} 条记录")
                    else:
                        print(f"❌ {symbol}: 时间范围内无数据")

                except Exception as e:
                    print(f"❌ {symbol}: 加载失败 - {e}")
            else:
                print(f"❌ {symbol}: 文件不存在")

        if self.raw_data:
            print(f"✅ 数据加载完成，共 {len(self.raw_data)} 只股票")
            return True
        else:
            print("❌ 没有成功加载任何数据")
            return False

    def load_yahoo_data(self):
        """加载Yahoo Finance数据"""
        try:
            import yfinance as yf
        except ImportError:
            print("❌ 需要安装 yfinance: pip install yfinance")
            return False

        # 确定时间范围
        if self.time_range.get('type') == 'all':
            start_date = '2020-01-01'
            end_date = datetime.now().strftime('%Y-%m-%d')
        else:
            start_date = self.time_range['start_date'].strftime('%Y-%m-%d')
            end_date = self.time_range['end_date'].strftime('%Y-%m-%d')

        for symbol in self.stock_pool:
            try:
                ticker = yf.Ticker(symbol)
                df = ticker.history(start=start_date, end=end_date)

                if len(df) > 0:
                    # 重命名列以匹配标准格式
                    df = df.reset_index()
                    df.columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
                    df = df[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']]

                    self.raw_data[symbol] = df
                    print(f"✅ {symbol}: {len(df)} 条记录")
                else:
                    print(f"❌ {symbol}: 无数据")

            except Exception as e:
                print(f"❌ {symbol}: 加载失败 - {e}")

        if self.raw_data:
            print(f"✅ Yahoo Finance数据加载完成，共 {len(self.raw_data)} 只股票")
            return True
        else:
            print("❌ 没有成功加载任何数据")
            return False

    def load_mock_api_data(self):
        """加载模拟API数据"""
        print("🎲 生成模拟API数据...")

        # 确定时间范围
        if self.time_range.get('type') == 'all':
            days = 365
        else:
            start_date = self.time_range['start_date']
            end_date = self.time_range['end_date']
            days = (end_date - start_date).days

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 工作日

        for i, symbol in enumerate(self.stock_pool):
            np.random.seed(hash(symbol) % 2**32)  # 基于股票名称的种子

            n = len(dates)
            returns = np.random.normal(0.001, 0.02, n)
            prices = 100 * np.exp(np.cumsum(returns))

            df = pd.DataFrame({
                'Date': dates,
                'Open': prices * np.random.uniform(0.99, 1.01, n),
                'High': prices * (1 + np.abs(np.random.normal(0, 0.01, n))),
                'Low': prices * (1 - np.abs(np.random.normal(0, 0.01, n))),
                'Close': prices,
                'Volume': np.random.randint(1000000, 10000000, n)
            })

            self.raw_data[symbol] = df
            print(f"✅ {symbol}: {len(df)} 条记录")

        print(f"✅ 模拟数据生成完成，共 {len(self.raw_data)} 只股票")
        return True

    def load_mock_data(self):
        """加载模拟数据"""
        return self.load_mock_api_data()  # 使用相同的逻辑

    def preview_data(self):
        """数据预览"""
        if not self.raw_data:
            print("❌ 没有数据可预览")
            return

        print("\n👀 数据预览")
        print("=" * 30)

        total_records = sum(len(df) for df in self.raw_data.values())
        print(f"📊 数据概览:")
        print(f"  股票数量: {len(self.raw_data)}")
        print(f"  总记录数: {total_records}")

        # 显示每只股票的数据概况
        print(f"\n📈 各股票数据详情:")
        for symbol, df in self.raw_data.items():
            start_date = df['Date'].min().strftime('%Y-%m-%d')
            end_date = df['Date'].max().strftime('%Y-%m-%d')
            print(f"  {symbol}: {len(df)} 条记录 ({start_date} 到 {end_date})")

        # 显示数据质量
        print(f"\n🔍 数据质量检查:")
        for symbol, df in self.raw_data.items():
            missing_data = df.isnull().sum().sum()
            duplicate_dates = df['Date'].duplicated().sum()

            status = "✅" if missing_data == 0 and duplicate_dates == 0 else "⚠️"
            print(f"  {symbol}: {status} 缺失值: {missing_data}, 重复日期: {duplicate_dates}")

        # 询问是否查看详细数据
        show_detail = input("\n是否查看详细数据样本? (y/n): ").strip().lower()
        if show_detail == 'y':
            self.show_data_samples()

    def show_data_samples(self):
        """显示数据样本"""
        for symbol, df in list(self.raw_data.items())[:3]:  # 最多显示3只股票
            print(f"\n📊 {symbol} 数据样本:")
            print(df.head().to_string(index=False))

            print(f"\n📈 {symbol} 基本统计:")
            numeric_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            print(df[numeric_cols].describe().round(2))

    def calculate_factors(self):
        """计算因子"""
        if not self.raw_data:
            print("❌ 没有数据可计算因子")
            return False

        print("\n🔧 计算因子")
        print("=" * 20)

        print("选择要计算的因子类型:")
        print("1. 基础因子（价格、收益率、移动平均）")
        print("2. 技术指标（RSI、MACD、布林带等）")
        print("3. 成交量因子（成交量相关指标）")
        print("4. 全部因子")

        choice = input("请选择 (1-4): ").strip()

        factor_types = []
        if choice == '1':
            factor_types = ['basic']
        elif choice == '2':
            factor_types = ['technical']
        elif choice == '3':
            factor_types = ['volume']
        elif choice == '4':
            factor_types = ['basic', 'technical', 'volume']
        else:
            print("❌ 无效选择")
            return False

        print(f"🔧 开始计算因子...")

        all_factors = []

        for symbol, df in self.raw_data.items():
            print(f"  计算 {symbol} 的因子...")

            stock_factors = self.compute_stock_factors(df, symbol, factor_types)
            if len(stock_factors) > 0:
                all_factors.append(stock_factors)

        if all_factors:
            self.factor_data = pd.concat(all_factors, ignore_index=True)
            # 移除包含过多NaN的行
            self.factor_data = self.factor_data.dropna(thresh=len(self.factor_data.columns) * 0.7)

            factor_cols = [col for col in self.factor_data.columns if col not in ['symbol', 'date']]
            print(f"✅ 因子计算完成: {len(factor_cols)} 个因子，{len(self.factor_data)} 条有效记录")

            return True
        else:
            print("❌ 因子计算失败")
            return False

    def compute_stock_factors(self, df, symbol, factor_types):
        """计算单只股票的因子"""
        df = df.sort_values('Date').reset_index(drop=True)

        if len(df) < 60:  # 确保有足够数据
            return pd.DataFrame()

        factors = pd.DataFrame()
        factors['symbol'] = symbol
        factors['date'] = df['Date']

        # 基础因子
        if 'basic' in factor_types:
            factors['close'] = df['Close']
            factors['return_1d'] = df['Close'].pct_change()
            factors['return_5d'] = df['Close'].pct_change(5)
            factors['return_20d'] = df['Close'].pct_change(20)

            # 移动平均
            for period in [5, 10, 20, 60]:
                factors[f'ma_{period}'] = df['Close'].rolling(period, min_periods=period).mean()
                factors[f'ma_{period}_ratio'] = df['Close'] / factors[f'ma_{period}']

        # 技术指标
        if 'technical' in factor_types:
            # RSI
            delta = df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14, min_periods=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14, min_periods=14).mean()
            rs = gain / (loss + 1e-10)
            factors['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            ema12 = df['Close'].ewm(span=12, min_periods=12).mean()
            ema26 = df['Close'].ewm(span=26, min_periods=26).mean()
            factors['macd'] = ema12 - ema26
            factors['macd_signal'] = factors['macd'].ewm(span=9, min_periods=9).mean()
            factors['macd_hist'] = factors['macd'] - factors['macd_signal']

            # 布林带
            bb_middle = df['Close'].rolling(20, min_periods=20).mean()
            bb_std = df['Close'].rolling(20, min_periods=20).std()
            factors['bb_upper'] = bb_middle + 2 * bb_std
            factors['bb_lower'] = bb_middle - 2 * bb_std
            factors['bb_width'] = factors['bb_upper'] - factors['bb_lower']
            factors['bb_position'] = (df['Close'] - factors['bb_lower']) / (factors['bb_width'] + 1e-10)

            # 波动率
            returns = df['Close'].pct_change()
            for period in [5, 20, 60]:
                factors[f'volatility_{period}d'] = returns.rolling(period, min_periods=period).std()

        # 成交量因子
        if 'volume' in factor_types:
            factors['volume'] = df['Volume']

            for period in [5, 20, 60]:
                factors[f'volume_ma_{period}'] = df['Volume'].rolling(period, min_periods=period).mean()
                factors[f'volume_ratio_{period}'] = df['Volume'] / (factors[f'volume_ma_{period}'] + 1)

            # 价量关系
            factors['price_volume'] = df['Close'] * df['Volume']
            factors['vwap_20'] = (df['Close'] * df['Volume']).rolling(20).sum() / df['Volume'].rolling(20).sum()

        # 只保留有效数据（从第60行开始）
        factors = factors.iloc[60:].copy()

        return factors

    def show_factor_library(self):
        """显示因子库"""
        if self.factor_data is None or len(self.factor_data) == 0:
            print("❌ 请先计算因子")
            return []

        print("\n📚 因子库")
        print("=" * 30)

        factor_cols = [col for col in self.factor_data.columns if col not in ['symbol', 'date']]

        # 按类别分组
        categories = {
            '价格收益': [f for f in factor_cols if any(x in f for x in ['close', 'return'])],
            '移动平均': [f for f in factor_cols if 'ma_' in f],
            '技术指标': [f for f in factor_cols if any(x in f for x in ['rsi', 'macd', 'bb_'])],
            '波动率': [f for f in factor_cols if 'volatility' in f],
            '成交量': [f for f in factor_cols if 'volume' in f or 'vwap' in f],
        }

        print(f"📊 因子总览 (共 {len(factor_cols)} 个因子):")

        for category, factors in categories.items():
            if factors:
                print(f"\n{category} ({len(factors)}个):")
                for i, factor in enumerate(factors, 1):
                    print(f"  {i:2d}. {factor}")

        return factor_cols

    def interactive_factor_selection(self):
        """交互式因子选择"""
        factor_cols = self.show_factor_library()

        if not factor_cols:
            return False

        print(f"\n🎛️ 因子选择")
        print("=" * 20)
        print("选择方式:")
        print("1. 自动选择代表性因子")
        print("2. 手动选择因子")
        print("3. 按类别选择")

        choice = input("请选择方式 (1-3): ").strip()

        if choice == '1':
            return self.auto_select_factors(factor_cols)
        elif choice == '2':
            return self.manual_select_factors(factor_cols)
        elif choice == '3':
            return self.category_select_factors(factor_cols)
        else:
            print("❌ 无效选择")
            return False

    def auto_select_factors(self, factor_cols):
        """自动选择代表性因子"""
        # 选择各类别的代表性因子
        auto_factors = []

        # 价格收益类
        for factor in ['return_5d', 'return_20d']:
            if factor in factor_cols:
                auto_factors.append(factor)

        # 移动平均类
        for factor in ['ma_20_ratio', 'ma_60_ratio']:
            if factor in factor_cols:
                auto_factors.append(factor)

        # 技术指标类
        for factor in ['rsi', 'macd', 'bb_position']:
            if factor in factor_cols:
                auto_factors.append(factor)

        # 波动率类
        for factor in ['volatility_20d']:
            if factor in factor_cols:
                auto_factors.append(factor)

        # 成交量类
        for factor in ['volume_ratio_20', 'vwap_20']:
            if factor in factor_cols:
                auto_factors.append(factor)

        if auto_factors:
            self.selected_factors = auto_factors
            print(f"✅ 自动选择了 {len(auto_factors)} 个代表性因子:")
            for factor in auto_factors:
                print(f"  - {factor}")
            return True
        else:
            print("❌ 没有找到合适的代表性因子")
            return False

    def manual_select_factors(self, factor_cols):
        """手动选择因子"""
        print("\n手动选择因子:")
        print("输入因子名称，用逗号分隔")
        print("或输入序号，用逗号分隔")

        # 显示所有因子及序号
        print("\n可选因子:")
        for i, factor in enumerate(factor_cols, 1):
            print(f"  {i:2d}. {factor}")

        while True:
            selection = input("\n请输入选择: ").strip()

            if not selection:
                print("❌ 请输入选择")
                continue

            selected = []

            # 尝试按序号解析
            if all(s.strip().isdigit() for s in selection.split(',')):
                indices = [int(s.strip()) - 1 for s in selection.split(',')]
                selected = [factor_cols[i] for i in indices if 0 <= i < len(factor_cols)]
            else:
                # 按名称解析
                names = [s.strip() for s in selection.split(',')]
                selected = [name for name in names if name in factor_cols]

            if selected:
                self.selected_factors = selected
                print(f"✅ 选择了 {len(selected)} 个因子:")
                for factor in selected:
                    print(f"  - {factor}")
                return True
            else:
                print("❌ 没有找到有效的因子，请重新输入")

    def category_select_factors(self, factor_cols):
        """按类别选择因子"""
        categories = {
            '价格收益': [f for f in factor_cols if any(x in f for x in ['close', 'return'])],
            '移动平均': [f for f in factor_cols if 'ma_' in f],
            '技术指标': [f for f in factor_cols if any(x in f for x in ['rsi', 'macd', 'bb_'])],
            '波动率': [f for f in factor_cols if 'volatility' in f],
            '成交量': [f for f in factor_cols if 'volume' in f or 'vwap' in f],
        }

        print("\n按类别选择因子:")
        for i, (category, factors) in enumerate(categories.items(), 1):
            if factors:
                print(f"  {i}. {category} ({len(factors)}个)")

        choice = input("请选择类别序号（用逗号分隔）: ").strip()

        try:
            indices = [int(i.strip()) - 1 for i in choice.split(',')]
            selected_categories = list(categories.keys())

            selected_factors = []
            for idx in indices:
                if 0 <= idx < len(selected_categories):
                    category = selected_categories[idx]
                    selected_factors.extend(categories[category])

            if selected_factors:
                self.selected_factors = selected_factors
                print(f"✅ 选择了 {len(selected_factors)} 个因子:")
                for factor in selected_factors:
                    print(f"  - {factor}")
                return True
            else:
                print("❌ 没有选择有效的类别")
                return False

        except ValueError:
            print("❌ 输入格式错误")
            return False

    def main_menu(self):
        """主菜单"""
        self.show_welcome()

        # 数据准备流程
        print("\n🚀 开始数据准备流程...")

        # 1. 选择数据源
        if not self.select_data_source():
            print("❌ 数据源设置失败")
            return

        # 2. 选择股票池
        if not self.select_stock_pool():
            print("❌ 股票池选择失败")
            return

        # 3. 设置时间范围
        if not self.set_time_range():
            print("❌ 时间范围设置失败")
            return

        # 4. 加载数据
        if not self.load_data():
            print("❌ 数据加载失败")
            return

        # 5. 数据预览
        self.preview_data()

        # 6. 计算因子
        if not self.calculate_factors():
            print("❌ 因子计算失败")
            return

        # 7. 因子选择
        if not self.interactive_factor_selection():
            print("❌ 因子选择失败")
            return

        # 进入主循环
        while True:
            print(f"\n" + "="*50)
            print("🎯 多因子研究工具 - 主菜单")
            print("="*50)
            print(f"📊 当前状态:")
            print(f"  数据源: {self.data_source['type']}")
            print(f"  股票数: {len(self.stock_pool)}")
            print(f"  数据记录: {sum(len(df) for df in self.raw_data.values())}")
            print(f"  选择因子: {len(self.selected_factors)}")

            print(f"\n📋 可用操作:")
            print("1. 🔍 分析选中因子")
            print("2. 📈 因子对比分析")
            print("3. 🧪 简单回测")
            print("4. 🎛️ 重新选择因子")
            print("5. 📊 重新选择数据")
            print("6. 💾 保存研究结果")
            print("0. 🚪 退出程序")

            choice = input("\n请选择操作 (0-6): ").strip()

            if choice == '1':
                from research_analysis import analyze_factors
                analyze_factors(self)
            elif choice == '2':
                from research_analysis import compare_factors
                compare_factors(self)
            elif choice == '3':
                from research_analysis import simple_backtest
                simple_backtest(self)
            elif choice == '4':
                self.interactive_factor_selection()
            elif choice == '5':
                from research_analysis import restart_data_selection
                restart_data_selection(self)
                return  # 重新开始整个流程
            elif choice == '6':
                from research_analysis import save_results
                save_results(self)
            elif choice == '0':
                print("👋 感谢使用多因子研究工具！")
                break
            else:
                print("❌ 无效选择，请重新输入")


def main():
    """主程序入口"""
    try:
        tool = InteractiveResearchTool()
        tool.main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，感谢使用！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
