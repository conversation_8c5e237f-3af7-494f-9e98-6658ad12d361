2025-07-13 00:19:18,838 - QuantStrategy - INFO - ============================================================
2025-07-13 00:19:18,838 - QuantStrategy - INFO - XGBoost多因子量化交易策略系统启动
2025-07-13 00:19:18,838 - QuantStrategy - INFO - ============================================================
2025-07-13 00:19:18,841 - QuantStrategy - INFO - ✅ 模块初始化完成
2025-07-13 00:19:18,841 - QuantStrategy - INFO - 📊 获取股票列表...
2025-07-13 00:19:18,842 - QuantStrategy - INFO - 📈 选择股票: ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH', '000858.SZ', '002415.SZ', '300059.SZ', '300750.SZ', '688981.SH']
2025-07-13 00:19:18,842 - QuantStrategy - INFO - 📅 数据时间范围: 2023-07-14 到 2025-07-13
2025-07-13 00:19:18,842 - QuantStrategy - INFO - 🔄 准备训练数据...
2025-07-13 00:19:20,016 - QuantStrategy - INFO - ✅ 数据准备完成: 2396 条记录, 63 个特征
2025-07-13 00:19:20,018 - QuantStrategy - INFO - 🔧 开始特征工程...
2025-07-13 00:19:21,470 - QuantStrategy - INFO - ✅ 特征工程完成: 60 个最终特征
2025-07-13 00:19:21,470 - QuantStrategy - INFO - 📊 分割数据集...
2025-07-13 00:19:21,471 - QuantStrategy - INFO - ✅ 数据分割完成:
2025-07-13 00:19:21,471 - QuantStrategy - INFO -    训练集: 1677 条记录
2025-07-13 00:19:21,471 - QuantStrategy - INFO -    验证集: 359 条记录
2025-07-13 00:19:21,471 - QuantStrategy - INFO -    测试集: 360 条记录
2025-07-13 00:19:21,471 - QuantStrategy - INFO - 🤖 开始模型训练...
2025-07-13 00:19:23,594 - QuantStrategy - INFO - ✅ 模型训练完成
2025-07-13 00:19:23,594 - QuantStrategy - INFO - 📈 模型评估...
2025-07-13 00:19:23,600 - QuantStrategy - INFO - 📊 测试集性能:
2025-07-13 00:19:23,600 - QuantStrategy - INFO -    mse: 0.0140
2025-07-13 00:19:23,600 - QuantStrategy - INFO -    rmse: 0.1183
2025-07-13 00:19:23,600 - QuantStrategy - INFO -    mae: 0.0961
2025-07-13 00:19:23,600 - QuantStrategy - INFO -    r2: -0.1664
2025-07-13 00:19:23,601 - QuantStrategy - INFO -    direction_accuracy: 0.5889
2025-07-13 00:19:23,601 - QuantStrategy - INFO - 🔍 特征重要性分析...
2025-07-13 00:19:23,602 - QuantStrategy - INFO - 📊 Top 10 重要特征:
2025-07-13 00:19:23,602 - QuantStrategy - INFO -    ema_60: 211.0000
2025-07-13 00:19:23,602 - QuantStrategy - INFO -    volume_std_60: 126.0000
2025-07-13 00:19:23,603 - QuantStrategy - INFO -    hv_60: 113.0000
2025-07-13 00:19:23,603 - QuantStrategy - INFO -    sma_120: 113.0000
2025-07-13 00:19:23,603 - QuantStrategy - INFO -    volume_ma_20: 103.0000
2025-07-13 00:19:23,603 - QuantStrategy - INFO -    volume_ma_60: 101.0000
2025-07-13 00:19:23,603 - QuantStrategy - INFO -    hv_20: 92.0000
2025-07-13 00:19:23,604 - QuantStrategy - INFO -    sma_60: 91.0000
2025-07-13 00:19:23,604 - QuantStrategy - INFO -    obv: 85.0000
2025-07-13 00:19:23,604 - QuantStrategy - INFO -    pe_ratio_div_sma_60: 76.0000
2025-07-13 00:19:23,604 - QuantStrategy - INFO - 🔄 执行交叉验证...
2025-07-13 00:19:24,216 - QuantStrategy - INFO - 📊 交叉验证结果:
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    mean_mse: 0.0105
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    std_mse: 0.0010
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    mean_rmse: 0.1024
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    std_rmse: 0.0050
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    mean_mae: 0.0814
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    std_mae: 0.0037
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    mean_r2: -0.1008
2025-07-13 00:19:24,216 - QuantStrategy - INFO -    std_r2: 0.1762
2025-07-13 00:19:24,217 - QuantStrategy - INFO -    mean_direction_accuracy: 0.5804
2025-07-13 00:19:24,217 - QuantStrategy - INFO -    std_direction_accuracy: 0.0990
2025-07-13 00:19:24,217 - QuantStrategy - INFO - 💾 保存模型和结果...
2025-07-13 00:19:24,232 - QuantStrategy - INFO - ✅ 模型和结果保存完成
2025-07-13 00:19:24,232 - QuantStrategy - INFO - 📄 生成策略报告...
2025-07-13 00:19:24,233 - QuantStrategy - INFO - 📄 策略报告已保存到: docs\strategy_report.md
2025-07-13 00:19:24,234 - QuantStrategy - INFO - ============================================================
2025-07-13 00:19:24,234 - QuantStrategy - INFO - 🎉 XGBoost多因子量化交易策略开发完成!
2025-07-13 00:19:24,234 - QuantStrategy - INFO - ============================================================
