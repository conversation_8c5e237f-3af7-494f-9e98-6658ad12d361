"""
交互式多因子研究工具
让用户手动选择因子、分析因子效果、对比不同组合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 导入项目模块
from data.data_loader import DataLoader
from data.factor_engine import FactorEngine
from data.data_processor import DataProcessor
from models.feature_engineer import FeatureEngineer
from models.xgboost_model import XGBoostModel
from utils.logger import LoggerManager

class InteractiveFactorResearch:
    """交互式多因子研究工具"""
    
    def __init__(self):
        self.logger = LoggerManager.get_logger("FactorResearch")
        self.data_loader = DataLoader()
        self.factor_engine = FactorEngine()
        self.data_processor = DataProcessor()
        
        # 数据存储
        self.raw_data = {}
        self.factor_data = None
        self.selected_factors = []
        self.factor_analysis_results = {}
        
        print("🎯 交互式多因子研究工具已启动")
        print("=" * 60)
    
    def show_welcome(self):
        """显示欢迎界面"""
        print("""
🎯 欢迎使用交互式多因子研究工具！

本工具将帮助您：
1. 📊 浏览和了解所有可用因子
2. 🔍 深入分析单个因子的特性
3. 📈 对比不同因子的效果
4. 🎛️ 手动选择因子组合
5. 🧪 交互式回测验证

让我们开始您的多因子研究之旅！
        """)
    
    def load_sample_data(self):
        """加载样本数据"""
        print("\n📊 正在加载样本数据...")
        
        # 获取股票列表
        try:
            stock_list = self.data_loader.get_stock_list()
            if stock_list.empty:
                symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH']
                print("使用默认股票列表")
            else:
                symbols = stock_list['ts_code'].head(5).tolist()
                print(f"从数据源获取股票列表: {symbols}")
        except:
            symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
            print("使用美股样本数据")
        
        # 设置时间范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        print(f"📅 数据时间范围: {start_date} 到 {end_date}")
        print(f"📈 股票池: {symbols}")
        
        # 加载数据
        for symbol in symbols:
            try:
                data = self.data_loader.get_stock_data(symbol, start_date, end_date)
                if not data.empty:
                    self.raw_data[symbol] = data
                    print(f"✅ {symbol}: {len(data)} 条记录")
                else:
                    print(f"❌ {symbol}: 无数据")
            except Exception as e:
                print(f"❌ {symbol}: 加载失败 - {e}")
        
        if not self.raw_data:
            print("❌ 无法加载任何数据，程序退出")
            return False
        
        print(f"\n✅ 数据加载完成，共 {len(self.raw_data)} 只股票")
        return True
    
    def calculate_all_factors(self):
        """计算所有因子"""
        print("\n🔧 正在计算所有因子...")
        
        all_factors = []
        
        for symbol, data in self.raw_data.items():
            print(f"计算 {symbol} 的因子...")
            
            # 计算技术因子
            tech_factors = self.factor_engine.calculate_technical_factors(data)
            tech_factors['symbol'] = symbol
            
            # 计算基本面因子
            fundamental_factors = self.factor_engine.calculate_fundamental_factors(data)
            
            # 合并因子
            factors = pd.concat([tech_factors, fundamental_factors], axis=1)
            factors['symbol'] = symbol
            
            all_factors.append(factors)
        
        # 合并所有股票的因子数据
        self.factor_data = pd.concat(all_factors, ignore_index=True)
        
        # 移除无效数据
        self.factor_data = self.factor_data.dropna()
        
        print(f"✅ 因子计算完成")
        print(f"📊 数据维度: {self.factor_data.shape}")
        print(f"📈 因子数量: {len([col for col in self.factor_data.columns if col != 'symbol'])}")
        
        return True
    
    def show_factor_library(self):
        """显示因子库"""
        if self.factor_data is None:
            print("❌ 请先计算因子数据")
            return
        
        print("\n📚 因子库总览")
        print("=" * 80)
        
        # 获取所有因子列
        factor_columns = [col for col in self.factor_data.columns if col != 'symbol']
        
        # 按类别分组显示
        categories = {
            '趋势类': [col for col in factor_columns if any(x in col for x in ['sma', 'ema', 'trend'])],
            '动量类': [col for col in factor_columns if any(x in col for x in ['rsi', 'momentum', 'roc'])],
            '波动率类': [col for col in factor_columns if any(x in col for x in ['volatility', 'hv', 'std'])],
            '成交量类': [col for col in factor_columns if any(x in col for x in ['volume', 'obv', 'vwap'])],
            '技术指标': [col for col in factor_columns if any(x in col for x in ['macd', 'bb', 'atr', 'cci'])],
            '基本面类': [col for col in factor_columns if any(x in col for x in ['pe', 'pb', 'roe', 'debt'])]
        }
        
        # 显示每个类别的因子
        for category, factors in categories.items():
            if factors:
                print(f"\n📊 {category} ({len(factors)}个):")
                for i, factor in enumerate(factors, 1):
                    print(f"  {i:2d}. {factor}")
        
        # 显示其他因子
        categorized_factors = set()
        for factors in categories.values():
            categorized_factors.update(factors)
        
        other_factors = [f for f in factor_columns if f not in categorized_factors]
        if other_factors:
            print(f"\n📊 其他因子 ({len(other_factors)}个):")
            for i, factor in enumerate(other_factors, 1):
                print(f"  {i:2d}. {factor}")
        
        print(f"\n📈 总计: {len(factor_columns)} 个因子")
        return factor_columns
    
    def interactive_factor_selection(self):
        """交互式因子选择"""
        factor_columns = self.show_factor_library()
        
        print("\n🎛️ 交互式因子选择")
        print("=" * 50)
        print("请选择您感兴趣的因子进行研究")
        print("输入格式: 因子名称 或 序号 (多个用逗号分隔)")
        print("输入 'all' 查看所有因子详情")
        print("输入 'done' 完成选择")
        print("输入 'clear' 清空已选择的因子")
        
        while True:
            print(f"\n当前已选择 {len(self.selected_factors)} 个因子: {self.selected_factors}")
            user_input = input("\n请输入您的选择: ").strip()
            
            if user_input.lower() == 'done':
                if self.selected_factors:
                    print(f"✅ 因子选择完成，共选择 {len(self.selected_factors)} 个因子")
                    break
                else:
                    print("❌ 请至少选择一个因子")
                    continue
            
            elif user_input.lower() == 'clear':
                self.selected_factors = []
                print("✅ 已清空选择")
                continue
            
            elif user_input.lower() == 'all':
                self.show_factor_details(factor_columns)
                continue
            
            # 解析用户输入
            selections = [s.strip() for s in user_input.split(',')]
            
            for selection in selections:
                if selection.isdigit():
                    # 按序号选择
                    idx = int(selection) - 1
                    if 0 <= idx < len(factor_columns):
                        factor = factor_columns[idx]
                        if factor not in self.selected_factors:
                            self.selected_factors.append(factor)
                            print(f"✅ 已添加因子: {factor}")
                        else:
                            print(f"⚠️ 因子已存在: {factor}")
                    else:
                        print(f"❌ 无效序号: {selection}")
                
                elif selection in factor_columns:
                    # 按名称选择
                    if selection not in self.selected_factors:
                        self.selected_factors.append(selection)
                        print(f"✅ 已添加因子: {selection}")
                    else:
                        print(f"⚠️ 因子已存在: {selection}")
                
                else:
                    print(f"❌ 未找到因子: {selection}")

    def show_factor_details(self, factor_columns):
        """显示因子详细信息"""
        print("\n📋 因子详细信息")
        print("=" * 80)

        for i, factor in enumerate(factor_columns, 1):
            # 计算基本统计信息
            factor_values = self.factor_data[factor].dropna()

            if len(factor_values) > 0:
                stats = {
                    '均值': factor_values.mean(),
                    '标准差': factor_values.std(),
                    '最小值': factor_values.min(),
                    '最大值': factor_values.max(),
                    '非空值数量': len(factor_values)
                }

                print(f"{i:2d}. {factor}")
                print(f"    均值: {stats['均值']:.4f}, 标准差: {stats['标准差']:.4f}")
                print(f"    范围: [{stats['最小值']:.4f}, {stats['最大值']:.4f}], 样本数: {stats['非空值数量']}")
            else:
                print(f"{i:2d}. {factor} - 无有效数据")

    def analyze_selected_factors(self):
        """分析选中的因子"""
        if not self.selected_factors:
            print("❌ 请先选择因子")
            return

        print(f"\n🔍 分析选中的 {len(self.selected_factors)} 个因子")
        print("=" * 60)

        # 为每个因子进行详细分析
        for factor in self.selected_factors:
            print(f"\n📊 分析因子: {factor}")
            print("-" * 40)

            factor_values = self.factor_data[factor].dropna()

            if len(factor_values) == 0:
                print("❌ 该因子无有效数据")
                continue

            # 基本统计
            stats = {
                '样本数': len(factor_values),
                '均值': factor_values.mean(),
                '标准差': factor_values.std(),
                '偏度': factor_values.skew(),
                '峰度': factor_values.kurtosis(),
                '最小值': factor_values.min(),
                '25%分位数': factor_values.quantile(0.25),
                '中位数': factor_values.median(),
                '75%分位数': factor_values.quantile(0.75),
                '最大值': factor_values.max()
            }

            print("📈 基本统计:")
            for key, value in stats.items():
                print(f"  {key}: {value:.4f}")

            # 存储分析结果
            self.factor_analysis_results[factor] = stats

            # 询问是否查看分布图
            show_plot = input(f"\n是否查看 {factor} 的分布图? (y/n): ").strip().lower()
            if show_plot == 'y':
                self.plot_factor_distribution(factor)

    def plot_factor_distribution(self, factor):
        """绘制因子分布图"""
        factor_values = self.factor_data[factor].dropna()

        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle(f'因子分析: {factor}', fontsize=16)

        # 直方图
        axes[0, 0].hist(factor_values, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('分布直方图')
        axes[0, 0].set_xlabel('因子值')
        axes[0, 0].set_ylabel('频数')

        # 箱线图
        axes[0, 1].boxplot(factor_values)
        axes[0, 1].set_title('箱线图')
        axes[0, 1].set_ylabel('因子值')

        # Q-Q图
        from scipy import stats
        stats.probplot(factor_values, dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('Q-Q图 (正态性检验)')

        # 时间序列图
        axes[1, 1].plot(factor_values.values)
        axes[1, 1].set_title('时间序列')
        axes[1, 1].set_xlabel('时间')
        axes[1, 1].set_ylabel('因子值')

        plt.tight_layout()
        plt.show()

    def compare_factors(self):
        """对比因子"""
        if len(self.selected_factors) < 2:
            print("❌ 请至少选择2个因子进行对比")
            return

        print(f"\n📈 对比选中的 {len(self.selected_factors)} 个因子")
        print("=" * 60)

        # 创建对比数据
        comparison_data = self.factor_data[self.selected_factors + ['symbol']].copy()

        # 计算相关性矩阵
        correlation_matrix = comparison_data[self.selected_factors].corr()

        print("\n🔗 因子相关性矩阵:")
        print(correlation_matrix.round(4))

        # 询问是否显示相关性热力图
        show_heatmap = input("\n是否显示相关性热力图? (y/n): ").strip().lower()
        if show_heatmap == 'y':
            plt.figure(figsize=(10, 8))
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                       square=True, fmt='.3f')
            plt.title('因子相关性热力图')
            plt.tight_layout()
            plt.show()

        # 因子统计对比
        print("\n📊 因子统计对比:")
        stats_comparison = pd.DataFrame()

        for factor in self.selected_factors:
            factor_values = comparison_data[factor].dropna()
            stats_comparison[factor] = [
                factor_values.mean(),
                factor_values.std(),
                factor_values.min(),
                factor_values.max(),
                factor_values.skew(),
                factor_values.kurtosis()
            ]

        stats_comparison.index = ['均值', '标准差', '最小值', '最大值', '偏度', '峰度']
        print(stats_comparison.round(4))

        return correlation_matrix

    def interactive_backtest(self):
        """交互式回测"""
        if not self.selected_factors:
            print("❌ 请先选择因子")
            return

        print(f"\n🧪 交互式回测")
        print("=" * 50)
        print(f"使用选中的 {len(self.selected_factors)} 个因子进行回测")

        # 准备数据
        print("\n📊 准备回测数据...")

        # 获取因子数据
        factor_data = self.factor_data[self.selected_factors + ['symbol']].copy()

        # 计算收益率作为目标变量
        print("计算目标收益率...")
        returns_data = []

        for symbol, data in self.raw_data.items():
            returns = data['Close'].pct_change(periods=5).shift(-5)  # 5日后收益率
            returns_df = pd.DataFrame({
                'symbol': symbol,
                'return_5d': returns
            })
            returns_data.append(returns_df)

        all_returns = pd.concat(returns_data, ignore_index=True)

        # 合并因子和收益率数据
        backtest_data = pd.merge(factor_data, all_returns, on='symbol', how='inner')
        backtest_data = backtest_data.dropna()

        if len(backtest_data) == 0:
            print("❌ 无有效的回测数据")
            return

        print(f"✅ 回测数据准备完成: {len(backtest_data)} 条记录")

        # 交互式参数设置
        print("\n⚙️ 回测参数设置:")

        # 选择模型类型
        print("1. 线性回归")
        print("2. XGBoost")
        print("3. 随机森林")

        model_choice = input("请选择模型类型 (1-3): ").strip()

        # 设置训练集比例
        train_ratio = input("请输入训练集比例 (默认0.7): ").strip()
        train_ratio = float(train_ratio) if train_ratio else 0.7

        # 执行回测
        self.run_backtest(backtest_data, model_choice, train_ratio)

    def run_backtest(self, data, model_choice, train_ratio):
        """执行回测"""
        print(f"\n🚀 开始回测...")

        # 准备特征和目标变量
        X = data[self.selected_factors]
        y = data['return_5d']

        # 数据分割
        split_idx = int(len(data) * train_ratio)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]

        print(f"训练集: {len(X_train)} 条记录")
        print(f"测试集: {len(X_test)} 条记录")

        # 选择模型
        if model_choice == '1':
            from sklearn.linear_model import LinearRegression
            model = LinearRegression()
            model_name = "线性回归"
        elif model_choice == '2':
            from xgboost import XGBRegressor
            model = XGBRegressor(random_state=42)
            model_name = "XGBoost"
        else:
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(random_state=42)
            model_name = "随机森林"

        # 训练模型
        print(f"🤖 训练{model_name}模型...")
        model.fit(X_train, y_train)

        # 预测
        y_pred = model.predict(X_test)

        # 计算评估指标
        from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        # 计算方向准确率
        direction_accuracy = np.mean(np.sign(y_test) == np.sign(y_pred))

        print(f"\n📊 {model_name}回测结果:")
        print(f"  MSE: {mse:.6f}")
        print(f"  MAE: {mae:.6f}")
        print(f"  R²: {r2:.4f}")
        print(f"  方向准确率: {direction_accuracy:.2%}")

        # 显示特征重要性
        if hasattr(model, 'feature_importances_'):
            importance_df = pd.DataFrame({
                'factor': self.selected_factors,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)

            print(f"\n🔍 特征重要性排序:")
            for idx, row in importance_df.iterrows():
                print(f"  {row['factor']}: {row['importance']:.4f}")

        # 询问是否显示预测结果图
        show_plot = input("\n是否显示预测结果图? (y/n): ").strip().lower()
        if show_plot == 'y':
            self.plot_backtest_results(y_test, y_pred, model_name)

    def plot_backtest_results(self, y_true, y_pred, model_name):
        """绘制回测结果图"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle(f'{model_name} 回测结果', fontsize=16)

        # 预测vs实际散点图
        axes[0, 0].scatter(y_true, y_pred, alpha=0.6)
        axes[0, 0].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('实际收益率')
        axes[0, 0].set_ylabel('预测收益率')
        axes[0, 0].set_title('预测 vs 实际')

        # 残差图
        residuals = y_true - y_pred
        axes[0, 1].scatter(y_pred, residuals, alpha=0.6)
        axes[0, 1].axhline(y=0, color='r', linestyle='--')
        axes[0, 1].set_xlabel('预测收益率')
        axes[0, 1].set_ylabel('残差')
        axes[0, 1].set_title('残差图')

        # 时间序列对比
        axes[1, 0].plot(y_true.values, label='实际', alpha=0.7)
        axes[1, 0].plot(y_pred, label='预测', alpha=0.7)
        axes[1, 0].set_xlabel('时间')
        axes[1, 0].set_ylabel('收益率')
        axes[1, 0].set_title('时间序列对比')
        axes[1, 0].legend()

        # 残差分布
        axes[1, 1].hist(residuals, bins=30, alpha=0.7, color='lightcoral')
        axes[1, 1].set_xlabel('残差')
        axes[1, 1].set_ylabel('频数')
        axes[1, 1].set_title('残差分布')

        plt.tight_layout()
        plt.show()

    def main_menu(self):
        """主菜单"""
        while True:
            print("\n" + "="*60)
            print("🎯 交互式多因子研究工具 - 主菜单")
            print("="*60)
            print("1. 📊 加载数据和计算因子")
            print("2. 📚 浏览因子库")
            print("3. 🎛️ 选择研究因子")
            print("4. 🔍 分析选中因子")
            print("5. 📈 对比因子效果")
            print("6. 🧪 交互式回测")
            print("7. 💾 保存研究结果")
            print("8. 📄 生成研究报告")
            print("0. 🚪 退出程序")

            choice = input("\n请选择功能 (0-8): ").strip()

            if choice == '1':
                if self.load_sample_data():
                    self.calculate_all_factors()

            elif choice == '2':
                self.show_factor_library()

            elif choice == '3':
                if self.factor_data is None:
                    print("❌ 请先加载数据和计算因子")
                else:
                    self.interactive_factor_selection()

            elif choice == '4':
                if not self.selected_factors:
                    print("❌ 请先选择因子")
                else:
                    self.analyze_selected_factors()

            elif choice == '5':
                if len(self.selected_factors) < 2:
                    print("❌ 请至少选择2个因子进行对比")
                else:
                    self.compare_factors()

            elif choice == '6':
                if not self.selected_factors:
                    print("❌ 请先选择因子")
                else:
                    self.interactive_backtest()

            elif choice == '7':
                self.save_research_results()

            elif choice == '8':
                self.generate_research_report()

            elif choice == '0':
                print("👋 感谢使用交互式多因子研究工具！")
                break

            else:
                print("❌ 无效选择，请重新输入")

    def save_research_results(self):
        """保存研究结果"""
        if not self.selected_factors:
            print("❌ 没有研究结果可保存")
            return

        print("\n💾 保存研究结果...")

        # 创建结果字典
        results = {
            'selected_factors': self.selected_factors,
            'factor_analysis': self.factor_analysis_results,
            'research_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data_summary': {
                'total_records': len(self.factor_data) if self.factor_data is not None else 0,
                'stocks': list(self.raw_data.keys()),
                'factor_count': len(self.selected_factors)
            }
        }

        # 保存到JSON文件
        import json
        filename = f"factor_research_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"✅ 研究结果已保存到: {filename}")

    def generate_research_report(self):
        """生成研究报告"""
        if not self.selected_factors:
            print("❌ 没有研究内容可生成报告")
            return

        print("\n📄 生成研究报告...")

        report_content = f"""# 多因子研究报告

## 研究概述
- **研究时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **研究股票**: {list(self.raw_data.keys())}
- **数据记录数**: {len(self.factor_data) if self.factor_data is not None else 0}
- **选择因子数**: {len(self.selected_factors)}

## 选择的因子
"""

        for i, factor in enumerate(self.selected_factors, 1):
            report_content += f"{i}. **{factor}**\n"

        if self.factor_analysis_results:
            report_content += "\n## 因子分析结果\n\n"

            for factor, stats in self.factor_analysis_results.items():
                report_content += f"### {factor}\n"
                report_content += f"- 样本数: {stats.get('样本数', 'N/A')}\n"
                report_content += f"- 均值: {stats.get('均值', 0):.4f}\n"
                report_content += f"- 标准差: {stats.get('标准差', 0):.4f}\n"
                report_content += f"- 偏度: {stats.get('偏度', 0):.4f}\n"
                report_content += f"- 峰度: {stats.get('峰度', 0):.4f}\n\n"

        report_content += """
## 研究建议

基于本次因子研究，建议：

1. **因子有效性验证**: 对选中的因子进行更长时间周期的验证
2. **因子组合优化**: 考虑因子间的相关性，构建最优因子组合
3. **风险控制**: 在实际应用中加入风险控制机制
4. **定期更新**: 定期重新评估因子的有效性

## 下一步工作

1. 扩大样本股票池
2. 增加更多类型的因子
3. 进行更详细的回测分析
4. 考虑交易成本和市场冲击

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 保存报告
        filename = f"factor_research_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ 研究报告已保存到: {filename}")


def main():
    """主程序入口"""
    try:
        # 创建研究工具实例
        research_tool = InteractiveFactorResearch()

        # 显示欢迎信息
        research_tool.show_welcome()

        # 启动主菜单
        research_tool.main_menu()

    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，感谢使用！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
