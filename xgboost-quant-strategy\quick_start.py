"""
快速启动 - 交互式多因子研究
最小化依赖，直接运行
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class QuickFactorResearch:
    """快速因子研究工具"""
    
    def __init__(self):
        self.data = {}
        self.factors = None
        self.selected_factors = []
        
        print("🎯 快速多因子研究工具")
        print("=" * 40)
    
    def generate_sample_data(self):
        """生成样本数据"""
        print("📊 生成样本数据...")
        
        symbols = ['股票A', '股票B', '股票C', '股票D', '股票E']
        end_date = datetime.now()
        start_date = end_date - timedelta(days=252)  # 一年数据
        
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 工作日
        
        all_data = []
        
        for i, symbol in enumerate(symbols):
            np.random.seed(i)  # 确保可重复
            
            n = len(dates)
            returns = np.random.normal(0.001, 0.02, n)
            prices = 100 * np.exp(np.cumsum(returns))
            
            # 生成OHLCV数据
            data = pd.DataFrame({
                'symbol': symbol,
                'date': dates,
                'open': prices * np.random.uniform(0.99, 1.01, n),
                'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n))),
                'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n))),
                'close': prices,
                'volume': np.random.randint(1000000, 10000000, n)
            })
            
            all_data.append(data)
        
        self.data = pd.concat(all_data, ignore_index=True)
        print(f"✅ 生成了 {len(symbols)} 只股票，{len(dates)} 个交易日的数据")
        
        return True
    
    def calculate_factors(self):
        """计算因子"""
        print("🔧 计算技术因子...")

        factor_data = []

        for symbol in self.data['symbol'].unique():
            stock_data = self.data[self.data['symbol'] == symbol].copy()
            stock_data = stock_data.sort_values('date').reset_index(drop=True)

            if len(stock_data) < 60:  # 确保有足够的数据
                continue

            factors = pd.DataFrame()
            factors['symbol'] = symbol
            factors['date'] = stock_data['date']

            # 价格因子
            factors['price'] = stock_data['close']
            factors['return_1d'] = stock_data['close'].pct_change()
            factors['return_5d'] = stock_data['close'].pct_change(5)
            factors['return_20d'] = stock_data['close'].pct_change(20)

            # 移动平均
            factors['ma5'] = stock_data['close'].rolling(5, min_periods=5).mean()
            factors['ma10'] = stock_data['close'].rolling(10, min_periods=10).mean()
            factors['ma20'] = stock_data['close'].rolling(20, min_periods=20).mean()
            factors['ma60'] = stock_data['close'].rolling(60, min_periods=60).mean()

            # 移动平均比率
            factors['ma5_ratio'] = stock_data['close'] / factors['ma5']
            factors['ma20_ratio'] = stock_data['close'] / factors['ma20']

            # RSI
            delta = stock_data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14, min_periods=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14, min_periods=14).mean()
            rs = gain / (loss + 1e-10)  # 避免除零
            factors['rsi'] = 100 - (100 / (1 + rs))

            # 波动率
            returns = stock_data['close'].pct_change()
            factors['volatility_5d'] = returns.rolling(5, min_periods=5).std()
            factors['volatility_20d'] = returns.rolling(20, min_periods=20).std()

            # 成交量因子
            factors['volume'] = stock_data['volume']
            factors['volume_ma5'] = stock_data['volume'].rolling(5, min_periods=5).mean()
            factors['volume_ma20'] = stock_data['volume'].rolling(20, min_periods=20).mean()
            factors['volume_ratio'] = stock_data['volume'] / (factors['volume_ma20'] + 1)

            # 价量关系
            factors['price_volume'] = stock_data['close'] * stock_data['volume']

            # 技术指标
            # MACD
            ema12 = stock_data['close'].ewm(span=12, min_periods=12).mean()
            ema26 = stock_data['close'].ewm(span=26, min_periods=26).mean()
            factors['macd'] = ema12 - ema26
            factors['macd_signal'] = factors['macd'].ewm(span=9, min_periods=9).mean()

            # 布林带
            bb_middle = stock_data['close'].rolling(20, min_periods=20).mean()
            bb_std = stock_data['close'].rolling(20, min_periods=20).std()
            factors['bb_upper'] = bb_middle + 2 * bb_std
            factors['bb_lower'] = bb_middle - 2 * bb_std
            factors['bb_width'] = factors['bb_upper'] - factors['bb_lower']
            factors['bb_position'] = (stock_data['close'] - factors['bb_lower']) / (factors['bb_width'] + 1e-10)

            # 动量因子
            factors['momentum_5'] = stock_data['close'] / stock_data['close'].shift(5) - 1
            factors['momentum_10'] = stock_data['close'] / stock_data['close'].shift(10) - 1
            factors['momentum_20'] = stock_data['close'] / stock_data['close'].shift(20) - 1

            # 只保留有效数据（从第60行开始，确保所有指标都有值）
            factors = factors.iloc[60:].copy()

            factor_data.append(factors)

        if factor_data:
            self.factors = pd.concat(factor_data, ignore_index=True)
            # 移除包含NaN的行
            self.factors = self.factors.dropna()
        else:
            self.factors = pd.DataFrame()

        factor_cols = [col for col in self.factors.columns if col not in ['symbol', 'date']]
        print(f"✅ 计算完成，共 {len(factor_cols)} 个因子，{len(self.factors)} 条有效记录")

        return factor_cols
    
    def show_factor_menu(self, factor_cols):
        """显示因子选择菜单"""
        print("\n📚 可用因子列表:")
        print("-" * 50)
        
        categories = {
            '价格类': [f for f in factor_cols if any(x in f for x in ['price', 'return', 'ma'])],
            '技术指标': [f for f in factor_cols if any(x in f for x in ['rsi', 'macd', 'bb'])],
            '波动率': [f for f in factor_cols if 'volatility' in f],
            '成交量': [f for f in factor_cols if 'volume' in f],
            '动量类': [f for f in factor_cols if 'momentum' in f],
        }
        
        for category, factors in categories.items():
            if factors:
                print(f"\n{category}:")
                for i, factor in enumerate(factors, 1):
                    print(f"  {i}. {factor}")
        
        return categories
    
    def interactive_selection(self):
        """交互式选择因子"""
        factor_cols = self.calculate_factors()
        categories = self.show_factor_menu(factor_cols)
        
        print(f"\n🎛️ 因子选择")
        print("输入因子名称，多个用逗号分隔")
        print("或输入 'auto' 自动选择代表性因子")
        
        while True:
            choice = input("\n请选择因子: ").strip()
            
            if choice.lower() == 'auto':
                # 自动选择代表性因子
                self.selected_factors = [
                    'return_5d', 'ma20_ratio', 'rsi', 'volatility_20d', 
                    'volume_ratio', 'macd', 'bb_position', 'momentum_10'
                ]
                print(f"✅ 自动选择了 {len(self.selected_factors)} 个代表性因子")
                break
            
            elif choice:
                selected = [f.strip() for f in choice.split(',')]
                valid_factors = [f for f in selected if f in factor_cols]
                
                if valid_factors:
                    self.selected_factors = valid_factors
                    print(f"✅ 选择了 {len(valid_factors)} 个因子: {valid_factors}")
                    break
                else:
                    print("❌ 没有找到有效的因子，请重新输入")
            else:
                print("❌ 请输入有效选择")
    
    def analyze_factors(self):
        """分析选中的因子"""
        if not self.selected_factors:
            print("❌ 请先选择因子")
            return
        
        print(f"\n🔍 分析 {len(self.selected_factors)} 个选中因子")
        print("=" * 50)
        
        factor_data = self.factors[self.selected_factors]
        
        # 基本统计
        print("\n📊 基本统计信息:")
        stats = factor_data.describe()
        print(stats.round(4))
        
        # 相关性分析
        print("\n🔗 因子相关性:")
        corr_matrix = factor_data.corr()
        print(corr_matrix.round(3))
        
        # 可视化选择
        show_plots = input("\n是否显示可视化图表? (y/n): ").strip().lower()
        if show_plots == 'y':
            self.plot_factor_analysis(factor_data, corr_matrix)
    
    def plot_factor_analysis(self, factor_data, corr_matrix):
        """绘制因子分析图"""
        n_factors = len(self.selected_factors)
        
        # 相关性热力图
        plt.figure(figsize=(10, 8))
        plt.subplot(2, 2, 1)
        
        try:
            import seaborn as sns
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')
        except ImportError:
            plt.imshow(corr_matrix, cmap='coolwarm', aspect='auto')
            plt.colorbar()
            
        plt.title('因子相关性热力图')
        
        # 因子分布
        plt.subplot(2, 2, 2)
        for factor in self.selected_factors[:4]:  # 最多显示4个
            plt.hist(factor_data[factor].dropna(), alpha=0.5, label=factor, bins=20)
        plt.title('因子分布')
        plt.legend()
        
        # 因子时间序列
        plt.subplot(2, 2, 3)
        for factor in self.selected_factors[:3]:  # 最多显示3个
            values = factor_data[factor].dropna()
            plt.plot(values.values, label=factor, alpha=0.7)
        plt.title('因子时间序列')
        plt.legend()
        
        # 因子统计
        plt.subplot(2, 2, 4)
        means = [factor_data[f].mean() for f in self.selected_factors]
        stds = [factor_data[f].std() for f in self.selected_factors]
        
        x_pos = range(len(self.selected_factors))
        plt.bar(x_pos, means, alpha=0.7)
        plt.xticks(x_pos, [f[:8] + '...' if len(f) > 8 else f for f in self.selected_factors], rotation=45)
        plt.title('因子均值')
        
        plt.tight_layout()
        plt.show()
    
    def simple_backtest(self):
        """简单回测"""
        if not self.selected_factors:
            print("❌ 请先选择因子")
            return

        if self.factors is None or len(self.factors) == 0:
            print("❌ 没有有效的因子数据")
            return

        print(f"\n🧪 简单回测")
        print("=" * 40)

        # 准备数据
        print("📊 准备回测数据...")

        # 计算未来收益作为目标
        future_returns = []

        for symbol in self.factors['symbol'].unique():
            # 获取因子数据
            symbol_factors = self.factors[self.factors['symbol'] == symbol].copy()
            symbol_factors = symbol_factors.sort_values('date').reset_index(drop=True)

            # 获取价格数据
            symbol_prices = self.data[self.data['symbol'] == symbol].copy()
            symbol_prices = symbol_prices.sort_values('date').reset_index(drop=True)

            if len(symbol_factors) == 0 or len(symbol_prices) == 0:
                continue

            # 计算5日后收益率
            returns_5d = symbol_prices['close'].pct_change(5).shift(-5)

            # 确保数据长度匹配
            min_len = min(len(symbol_factors), len(returns_5d))
            if min_len > 0:
                symbol_factors = symbol_factors.iloc[:min_len].copy()
                symbol_factors['future_return'] = returns_5d.iloc[:min_len].values
                future_returns.append(symbol_factors)

        if not future_returns:
            print("❌ 无法计算未来收益率")
            return

        backtest_data = pd.concat(future_returns, ignore_index=True)
        backtest_data = backtest_data.dropna()

        if len(backtest_data) == 0:
            print("❌ 无有效回测数据")
            return

        print(f"✅ 回测数据准备完成: {len(backtest_data)} 条记录")

        # 简单线性回归
        try:
            from sklearn.linear_model import LinearRegression
            from sklearn.metrics import mean_squared_error, r2_score
        except ImportError:
            print("❌ 缺少scikit-learn库，请安装: pip install scikit-learn")
            return

        X = backtest_data[self.selected_factors]
        y = backtest_data['future_return']

        # 检查数据有效性
        if X.isnull().any().any() or y.isnull().any():
            print("❌ 数据包含空值，无法进行回测")
            return

        # 分割数据
        split_idx = int(len(X) * 0.7)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]

        if len(X_train) == 0 or len(X_test) == 0:
            print("❌ 数据量不足，无法分割训练集和测试集")
            return

        # 训练模型
        print("🤖 训练线性回归模型...")
        model = LinearRegression()
        model.fit(X_train, y_train)

        # 预测
        y_pred = model.predict(X_test)

        # 评估
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        direction_acc = np.mean(np.sign(y_test) == np.sign(y_pred))

        print(f"\n📊 回测结果:")
        print(f"  样本数: 训练集 {len(X_train)}, 测试集 {len(X_test)}")
        print(f"  MSE: {mse:.6f}")
        print(f"  R²: {r2:.4f}")
        print(f"  方向准确率: {direction_acc:.2%}")

        # 特征重要性
        print(f"\n🔍 因子重要性:")
        importance = np.abs(model.coef_)
        factor_importance = list(zip(self.selected_factors, importance))
        factor_importance.sort(key=lambda x: x[1], reverse=True)

        for factor, imp in factor_importance:
            print(f"  {factor}: {imp:.4f}")

        return {
            'mse': mse,
            'r2': r2,
            'direction_accuracy': direction_acc,
            'feature_importance': factor_importance
        }
    
    def main_menu(self):
        """主菜单"""
        print("\n🎯 快速多因子研究工具")
        
        # 自动生成数据和计算因子
        if self.generate_sample_data():
            self.interactive_selection()
            
            while True:
                print(f"\n当前选择的因子: {self.selected_factors}")
                print("\n选择操作:")
                print("1. 🔍 分析选中因子")
                print("2. 🧪 简单回测")
                print("3. 🎛️ 重新选择因子")
                print("0. 🚪 退出")
                
                choice = input("\n请选择 (0-3): ").strip()
                
                if choice == '1':
                    self.analyze_factors()
                elif choice == '2':
                    self.simple_backtest()
                elif choice == '3':
                    self.interactive_selection()
                elif choice == '0':
                    print("👋 感谢使用！")
                    break
                else:
                    print("❌ 无效选择")

def main():
    """主程序"""
    try:
        research = QuickFactorResearch()
        research.main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 程序被中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
